import React, { useMemo } from 'react'
import { Edit, FolderInput, FolderPlus, Trash } from 'lucide-react'
import { ResourceSource } from '@/types/resources'
import { useItemActions } from '@/hooks/useItemActions'
import { TreeNode } from '@/components/TreeList'
import { FolderAction } from '@/components/material/MediaItem'

export const useFolderActions = (
  isLocal: boolean,
  type: ResourceSource,
  childFolders: TreeNode[],
  setMoveType: (type: ResourceSource) => void,
  setMoveId: (id: string) => void,
  setMoveDialogOpen: (open: boolean) => void,
) => {
  const { createItem, renameItem, deleteItem, deleteLocalItem } = useItemActions()

  return useMemo<FolderAction[]>(() => {
    const actions: FolderAction[] = [
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: '移动',
        onClick: nodeId => {
          setMoveType(type)
          setMoveId(nodeId)
          setMoveDialogOpen(true)
        },
      },
      {
        icon: <Edit className="w-4 h-4" />,
        label: '重命名',
        onClick: (nodeId, _parentId, label) =>
          renameItem(type, nodeId, label!, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }),
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: '删除',
        onClick: (nodeId, _parentId, label) => {
          if (isLocal) {
            return deleteLocalItem(type, nodeId, label!)
          }
          return deleteItem(type, nodeId, label!)
        },
      },
    ]
    if (!isLocal) {
      actions.unshift({
        icon: <FolderPlus className="w-4 h-4" />,
        label: '新建文件夹',
        onClick: nodeId =>
          createItem(type, nodeId, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }, childFolders),
      })
    }
    return actions
  }, [createItem, renameItem, deleteItem, deleteLocalItem, setMoveType, setMoveId, setMoveDialogOpen, isLocal])
}
