import React, { memo, useCallback, useEffect, useMemo, useState } from 'react'
import {
  useInfiniteQueryLocalSoundList,
  useInfiniteQuerySoundUnified,
  useQuerySoundCategory,
  useQuerySoundDirList
} from '@/hooks/queries/useQuerySound'
import { ResourceSource, SoundResource } from '@/types/resources'
import { ResourcePanelLayout, ResourceTab } from '../components/resource-panel-layout'
import { Music } from 'lucide-react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { InfiniteResourceList } from '@/components/InfiniteResourceList'
import { AudioResourceItem } from '../components/audio-resource-item'
import { filterByDuration } from '../../components/common/duration-filter'
import { ResourceTabType } from '@/modules/video-editor/resource-plugin-system'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { UploadedFile } from '@/components/ui/file-uploader'
import { ResourceModule } from '@/libs/request/api/resource'
import { LocalResourcePanel } from '@/modules/video-editor/resource-plugin-system/components/local-resource-panel'
import { DraggableLocalAudioItem } from '../components/local-audio-item'
import { useAddAudioToTimeLine } from '../../hooks/useAddAudioToTimeLine'

const SoundResourceTabs: ResourceTab[] = [
  {
    label: '音效库',
    value: ResourceTabType.ONLINE,
    showSearch: true,
    showCategorySelector: true,
    searchPlaceholder: '搜索音效，按回车键搜索',
  },
  {
    label: '我的音效',
    value: ResourceTabType.LOCAL,
    showSearch: true,
    showCategorySelector: false,
    searchPlaceholder: '搜索本地音效，按回车键搜索',
  },
]

export function SoundEffectPanel() {
  const { data: soundCategory } = useQuerySoundCategory()

  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [searchKey, setSearchKey] = useState<string>('')
  const [selectedDuration, setSelectedDuration] = useState<string>('')
  const infiniteQueryResult = useInfiniteQuerySoundUnified({
    pageSize: 12,
    selectedCategory: selectedCategory,
    keyword: searchKey,
  })

  //本地资源
  const queryClient = useQueryClient()
  const { data: dirList } = useQuerySoundDirList() // 请求本地目录
  const [currentFolderId, setCurrentFolderId] = useState('') //当前目录
  const { handleAddAudioToTimeline } = useAddAudioToTimeLine()

  //更新当前目录
  useEffect(() => {
    if (dirList?.length && !currentFolderId) {
      setCurrentFolderId(dirList[0].id)
    }
  }, [dirList, currentFolderId])

  const onRefreshLocalResource = useCallback(async () => {
    await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_SOUND_FOLDER_LIST] })
  }, [queryClient])

  const handleFolderChange = useCallback(
    async (folderId: string) => {
      setCurrentFolderId(folderId)
      await onRefreshLocalResource()
    },
    [onRefreshLocalResource],
  )

  const filteredInfiniteQueryResult = useMemo(() => {
    if (!selectedDuration || !infiniteQueryResult.data) {
      return infiniteQueryResult
    }

    const filteredData = {
      ...infiniteQueryResult.data,
      pages: infiniteQueryResult.data.pages.map(page => ({
        ...page,
        list: filterByDuration(page.list, selectedDuration),
        total: filterByDuration(page.list, selectedDuration).length,
      })),
    }

    return {
      ...infiniteQueryResult,
      data: filteredData,
    }
  }, [infiniteQueryResult, selectedDuration])

  const renderSoundItem = (item: SoundResource.Sound, index: number) => (
    <div className="relative">
      <AudioResourceItem
        key={index}
        item={item}
        icon={<Music className="w-8 h-8" />}
        resourceType={ResourceType.SOUND}
        showCollectionButton={true}
      />
    </div>
  )

  const renderSoundContent = useCallback(() => {
    return (
      <InfiniteResourceList
        queryResult={filteredInfiniteQueryResult}
        renderItem={renderSoundItem}
        emptyText="该分类暂无音效"
        loadingText="加载音效中..."
        itemsContainerClassName="grid grid-cols-4 gap-3 pt-3 pb-3"
      />
    )
  }, [filteredInfiniteQueryResult, renderSoundItem])

  const renderLocalSoundContent = useCallback(() => {
    const { data: localResources } = useInfiniteQueryLocalSoundList({
      pageSize: 30,
      folderUuid: currentFolderId,
      keyword: searchKey,
    })

    //上传文件回调
    const handleUploadComplete = useCallback(async (uploaded: UploadedFile[]) => {
      console.log('执行创建')

      // 上传成功的文件数组
      for (const file of uploaded) {
        try {
          await ResourceModule.voice.localCreate({
            folderUuid: file.folderUuid,
            title: file.fileName || file.file.name,
            fileMd5: file.fileMd5 ?? '',
            contentType: 'voice',
            objectId: file.objectId ?? '',
          })
          console.log('文件已成功上传并创建！')
        } catch (err) {
          console.error('上传失败：', err)
        }
      }

      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_SOUND_FOLDER_LIST] })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LOCAL_SOUND_LIST] })
    }, [])
    return (
      <LocalResourcePanel<SoundResource.SoundLocal>
        dirList={dirList || []}
        currentFolderId={currentFolderId}
        onFolderChange={handleFolderChange}
        resourceType={ResourceSource.LOCAL_SOUND}
        resourceFolderType={ResourceSource.LOCAL_SOUND_FOLDER}
        fileUploadTypes={['audio/mpeg']}
        searchKey={searchKey}
        onAddToTimeline={item => handleAddAudioToTimeline(item.content.durationMsec, item.content.itemUrl, item.title)}
        resources={localResources}
        onUploadComplete={handleUploadComplete}
        emptyText="暂无本地音效"
        renderResourceItem={(resource: SoundResource.SoundLocal) => (
          <DraggableLocalAudioItem
            materialType={ResourceSource.LOCAL_SOUND}
            resource={resource}
            key={resource.fileId}
            audioUrl={resource.content.itemUrl}
            durations={resource.content.durationMsec}
            title={resource.title}
            id={resource.fileId}
            icon={<Music className="w-8 h-8" />}
            resourceType={ResourceType.SOUND}
          />
        )}
      />
    )
  }, [dirList, currentFolderId, handleFolderChange, handleAddAudioToTimeline])

  const hasSounds = useMemo(() => {
    const firstPage = infiniteQueryResult.data?.pages?.[0]
    return !!firstPage && firstPage.list.length > 0
  }, [infiniteQueryResult.data])

  // 配置标签页内容
  const tabsWithContent = useMemo(() => {
    return SoundResourceTabs.map(tab => ({
      ...tab,
      renderContent: () => {
        switch (tab.value) {
          case ResourceTabType.ONLINE:
            return renderSoundContent()
          case ResourceTabType.LOCAL:
            return renderLocalSoundContent()
          default:
            return null
        }
      },
      isEmpty: tab.value === ResourceTabType.ONLINE ? !hasSounds : false,
      emptyText: tab.value === ResourceTabType.ONLINE ? '该分类暂无音效' : '暂无本地音效',
    }))
  }, [renderSoundContent, renderLocalSoundContent, hasSounds])

  return (
    <ResourcePanelLayout
      tabs={tabsWithContent}
      defaultTab={ResourceTabType.ONLINE}
      categories={soundCategory}
      selectedCategory={selectedCategory}
      onCategoryChange={setSelectedCategory}
      searchKey={searchKey}
      onSearchChange={setSearchKey}
      selectedDuration={selectedDuration}
      onDurationChange={setSelectedDuration}
    />
  )
}

export default memo(SoundEffectPanel)
