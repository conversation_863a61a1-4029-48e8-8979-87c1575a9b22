import React, { useEffect, useMemo, useRef, useState } from 'react'
import type { VideoOverlay } from '@clipnest/remotion-shared/types'
import { useLoadTileImageInfo } from '@/hooks/material/useLoadTileImageInfo'
import { Tile<PERSON>enderer } from '@/components/material/TileRenderer'
import { TIMELINE_TRACK_HEIGHT } from '@/modules/video-editor/constants'

/**
 * Props for the TimelineKeyframes component
 */
interface VideoOverlayKeyframeProps {
  /** The overlay object containing video/animation data */
  overlay: VideoOverlay
}

export const VideoOverlayKeyframe: React.FC<VideoOverlayKeyframeProps> = ({ overlay }) => {
  if (!overlay.originalMeta || !overlay.originalMeta.tileUrl) return null

  const { tileUrl, height, width } = overlay.originalMeta
  const aspectRatio = width && height ? width / height : 1

  const [containerWidth, setContainerWidth] = useState<number>()

  const elRef = useRef<HTMLDivElement | null>(null)

  const totalSlots = useMemo(() => {
    if (!containerWidth) return 0
    return Math.ceil(containerWidth / (aspectRatio * TIMELINE_TRACK_HEIGHT))
  }, [containerWidth, aspectRatio])

  const tileInfo = useLoadTileImageInfo(tileUrl)

  const slots = useMemo(() => {
    if (!tileInfo || !tileInfo) return []

    const { totalFrames: tileFrameCount } = tileInfo
    const { durationInFrames: videoDurationInFrames } = overlay.originalMeta

    // 获取去片头和去片尾的帧数
    const trimStartFrames = overlay.trimStartFrames ?? 0
    const trimEndFrames = overlay.trimEndFrames ?? 0

    // 计算实际可用的视频帧范围（基于视频实际总帧数）
    const availableStartFrame = trimStartFrames
    const availableEndFrame = videoDurationInFrames - trimEndFrames
    const availableFrameCount = Math.max(0, availableEndFrame - availableStartFrame)

    // 如果没有可用帧，返回空数组
    if (availableFrameCount <= 0) return []

    return Array.from({ length: totalSlots }, (_, index) => {
      // 在可用帧范围内进行线性分布
      const progress = totalSlots > 1 ? index / (totalSlots - 1) : 0
      const videoFrameIndex = availableStartFrame + Math.floor(progress * (availableFrameCount - 1))

      // 将视频帧索引映射到瓦片图帧索引
      // 瓦片图帧是从整个视频中采样的，需要按比例映射
      const tileFrameIndex = Math.floor((videoFrameIndex / videoDurationInFrames) * (tileFrameCount - 1))

      return Math.min(tileFrameIndex, tileFrameCount - 1)
    })
  }, [totalSlots, tileInfo, overlay.originalMeta.durationInFrames, overlay.trimStartFrames, overlay.trimEndFrames])

  useEffect(() => {
    const measureContainerSize = () => {
      if (elRef.current) {
        const { clientWidth } = elRef.current
        console.log('current width:', clientWidth)
        setContainerWidth(clientWidth)
      }
    }

    // Listen the element's size change:
    const observer = new ResizeObserver(measureContainerSize)
    if (elRef.current) {
      observer.observe(elRef.current)
    }

    measureContainerSize()

    return () => {
      observer.disconnect()
    }
  }, [])

  return (
    <div
      ref={elRef}
      className="VideoOverlayKeyframe flex overflow-hidden size-full relative"
    >
      {tileInfo && (
        <div className="flex h-full w-full">
          {slots.map((frameNumber, index) => (
            <div
              key={index}
              style={{
                aspectRatio,
                height: TIMELINE_TRACK_HEIGHT,
                position: 'relative'
              }}
              className="border-r border-gray-500"
            >
              <TileRenderer
                tileInfo={tileInfo}
                aspectRatio={aspectRatio}
                currentFrame={frameNumber}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
